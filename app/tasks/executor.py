import subprocess
import logging
import time
import os
import signal
from datetime import datetime
from typing import Dict, <PERSON>, Tuple, Optional, Any
import threading

from celery.exceptions import SoftTimeLimitExceeded
from app.db import operations
from app.models.task import TaskStatus

logger = logging.getLogger(__name__)

# Dictionary to store running processes
running_processes: Dict[str, subprocess.Popen] = {}
process_lock = threading.Lock()


def _kill_and_wait(proc: subprocess.Popen, sig=signal.SIGTERM, timeout: float = 5.0):
    """
    Send *sig* to the process-group of *proc* and reap it.
    Falls back to SIGKILL if still alive after *timeout*.
    """
    try:
        if proc.poll() is None:  # still running
            os.killpg(proc.pid, sig)               # terminate whole group
            t0 = time.time()
            while proc.poll() is None and (time.time() - t0) < timeout:
                time.sleep(0.1)

        if proc.poll() is None:                    # still not dead? nuke it.
            os.killpg(proc.pid, signal.SIGKILL)
        proc.wait()                                # reap -> no zombie
    except ProcessLookupError:
        # already gone
        pass


def execute_command(job_id: str,
                    command: List[str],
                    timeout: int = 3600) -> Tuple[bool, List[str]]:
    """
    Execute *command* and stream combined stdout/stderr lines back.
    Guarantees the child is reaped even if the task is revoked or times out,
    preventing zombie processes.
    """
    proc: Optional[subprocess.Popen] = None
    raw_output: List[str] = []

    def _sigterm_handler(signum, frame):
        # Celery may SIGTERM the worker; propagate to child pgid and reap.
        if proc:
            _kill_and_wait(proc, signal.SIGTERM)

    # register a temporary SIGTERM handler
    previous_handler = signal.signal(signal.SIGTERM, _sigterm_handler)

    try:
        # ensure the DB operation row exists
        operation = operations.get_operation(job_id) or operations.create_operation(
            feature="unknown", parameters={}, job_id=job_id
        )
        if "raw_output" not in operation:
            operations.update_operation(job_id=job_id, raw_output=[])

        start_time = time.time()

        # launch in its own session (= new process-group)
        with subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            start_new_session=True,  # ← key to later killpg
        ) as proc:
            # expose to other helpers
            with process_lock:
                running_processes[job_id] = proc

            # read line-by-line
            while True:
                # timeout check
                if time.time() - start_time > timeout:
                    _kill_and_wait(proc, signal.SIGTERM)
                    msg = f"Task timed out after {timeout} s"
                    logger.error(msg)
                    operations.update_operation(job_id=job_id,
                                                status=TaskStatus.KILLED,
                                                end_time=datetime.now(),
                                                error=msg)
                    return False, [msg]

                line = proc.stdout.readline()
                if not line:
                    if proc.poll() is not None:   # finished
                        break
                    time.sleep(0.05)
                    continue

                raw_output.append(line.rstrip())

            return_code = proc.wait()

        # remove from registry (inside `with` ensures child already reaped)
        with process_lock:
            running_processes.pop(job_id, None)

        # persist output only on non-zero exit
        if return_code != 0:
            for line in raw_output:
                operations.add_raw_output(job_id, line)

        return return_code == 0, raw_output

    except SoftTimeLimitExceeded:                  # optional, if you configure Celery soft-limit
        if proc:
            _kill_and_wait(proc, signal.SIGTERM)
        msg = f"Soft time-limit exceeded after {timeout} s"
        logger.warning(msg)
        operations.update_operation(job_id=job_id,
                                    status=TaskStatus.KILLED,
                                    end_time=datetime.now(),
                                    error=msg)
        return False, [msg]

    except Exception as e:
        logger.exception(f"Error executing command: {e}")
        if proc:
            _kill_and_wait(proc, signal.SIGTERM)
        operations.update_operation(job_id=job_id,
                                    status=TaskStatus.FAILED,
                                    end_time=datetime.now(),
                                    error=str(e))
        return False, [str(e)]

    finally:
        # restore old SIGTERM handler
        signal.signal(signal.SIGTERM, previous_handler)
        # make doubly sure registry is clean
        with process_lock:
            running_processes.pop(job_id, None)


def kill_task(job_id: str) -> bool:
    """Kill a running task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGTERM
        process.terminate()

        # Wait for process to terminate
        for _ in range(5):
            if process.poll() is not None:
                break
            time.sleep(0.1)

        # If process is still running, send SIGKILL
        if process.poll() is None:
            process.kill()
            process.wait()

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.KILLED,
            end_time=datetime.now()
        )

        # Remove process from running processes
        with process_lock:
            if job_id in running_processes:
                del running_processes[job_id]

        return True

    except Exception as e:
        logger.exception(f"Error killing task {job_id}: {e}")
        return False

def suspend_task(job_id: str) -> bool:
    """Suspend a running task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGSTOP
        os.kill(process.pid, signal.SIGSTOP)

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.SUSPENDED
        )
        return True
    except Exception as e:
        logger.exception(f"Error suspending task {job_id}: {e}")
        return False

def resume_task(job_id: str) -> bool:
    """Resume a suspended task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGCONT
        os.kill(process.pid, signal.SIGCONT)

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING
        )
        return True
    except Exception as e:
        logger.exception(f"Error resuming task {job_id}: {e}")
        return False

def get_operation_status(job_id: str) -> Optional[Dict[str, Any]]:
    """Get operation status, combining database and Celery information"""
    return operations.get_operation_status(job_id)

def get_operation(job_id: str) -> Optional[Dict[str, Any]]:
    """Get operation by ID"""
    return operations.get_operation(job_id)

def handle_task_success(job_id: str, structured_output: Dict[str, Any], message: str, count: int = None) -> Dict[str, Any]:
    """Helper function to handle successful task completion"""
    operations.update_operation(
        job_id=job_id,
        status=TaskStatus.COMPLETED,
        structured_output=structured_output,
        raw_output=[],  # Clear raw output on success
        end_time=datetime.now(),
        progress_perc=100
    )

    result = {
        "job_id": job_id,
        "success": True,
        "message": message
    }
    if count is not None:
        result["count"] = count
    return result

def handle_task_failure(job_id: str, error_message: str) -> Dict[str, Any]:
    """Helper function to handle task failure"""
    operations.update_operation(
        job_id=job_id,
        status=TaskStatus.FAILED,
        error=error_message,
        end_time=datetime.now()
    )

    return {
        "job_id": job_id,
        "success": False,
        "message": error_message
    }

def handle_task_exception(job_id: str, exception: Exception, context: str) -> Dict[str, Any]:
    """Helper function to handle task exceptions"""
    error_message = str(exception)
    logger.exception(f"Error in {context}: {exception}")
    return handle_task_failure(job_id, error_message)
