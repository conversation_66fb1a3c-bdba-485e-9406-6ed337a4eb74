"""
Pastes search tasks using Google Custom Search Engine.
"""
import logging
import os
import requests
import time
import random
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import handle_task_success, handle_task_failure, handle_task_exception
from app.utils.secret_detection import SecretDetection
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Google CSE API configuration
GOOGLE_CSE_API_URL = "https://www.googleapis.com/customsearch/v1"
GOOGLE_CSE_API_KEY = os.getenv("GOOGLE_CSE_API_KEY", "")
GOOGLE_CSE_ID = "e7ea81b606417454c"
CSE_MIN_DELAY = int(os.getenv("CSE_MIN_DELAY", 1))
CSE_MAX_DELAY = int(os.getenv("CSE_MAX_DELAY", 3))


def query_google_cse_api(domain: str, start_index: int = 1) -> Optional[Dict[str, Any]]:
    """
    Query Google Custom Search Engine API for pastes containing the domain.

    Args:
        domain: Domain to search for
        start_index: Starting index for pagination (1-based)

    Returns:
        API response as JSON or None if the request fails
    """
    try:
        # Add random delay to avoid flooding the API
        delay = random.uniform(CSE_MIN_DELAY, CSE_MAX_DELAY)
        logger.info(f"Waiting {delay:.2f} seconds before querying Google CSE API")
        time.sleep(delay)

        # Prepare query parameters
        params = {
            "key": GOOGLE_CSE_API_KEY,
            "cx": GOOGLE_CSE_ID,
            "exactTerms": domain,
            # "q": domain,
            "sort": "date",
            "start": start_index,
            "num": 10  # Google CSE returns max 10 results per request
        }

        # Make request
        logger.info(f"Querying Google CSE API: {GOOGLE_CSE_API_URL} with params {params}")
        response = requests.get(GOOGLE_CSE_API_URL, params=params, timeout=30)

        # Check if request was successful
        if response.status_code == 200:
            return response.json()
        else:
            error_msg = f"Google CSE API request failed with status code {response.status_code}: {response.text}"
            logger.error(error_msg)
            # Raise an exception to ensure the task fails
            raise Exception(error_msg)
    except Exception as e:
        logger.exception(f"Error querying Google CSE API: {e}")
        # Re-raise the exception to ensure the task fails
        raise


def fetch_paste_content(url: str) -> Optional[str]:
    """
    Fetch the raw content of a paste from various paste services.

    Args:
        url: URL of the paste

    Returns:
        Raw text content of the paste or None if failed
    """
    try:
        # Add delay to avoid flooding paste services
        delay = random.uniform(0.5, 1.5)
        time.sleep(delay)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            return response.text
        else:
            logger.warning(f"Failed to fetch paste content from {url}: {response.status_code}")
            return None
    except Exception as e:
        logger.warning(f"Error fetching paste content from {url}: {e}")
        return None


def convert_to_raw_url(url: str):
    """
    Convert paste URLs to their raw content URLs when possible.
    Args:
        url: Original paste URL
    Returns:
        Raw content URL if conversion mapping exists, False if no mapping available
    """
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        path = parsed.path.strip('/')
        
        # Domain-to-conversion mapping
        conversions = {
            'pastebin.com': lambda: f"https://pastebin.com/raw/{path.split('/')[-1]}",
            'pastebin.pl': lambda: f"https://pastebin.pl/view/raw/{path.split('/')[-1]}",
            'etextpad.com': lambda: f"https://etextpad.com/raw/{path.split('/')[-1]}",
            'paste.ee': lambda: f"https://paste.ee/r/{path.split('/')[-1]}",
            'pastecode.io': lambda: f"https://api.pastecode.io/anon/raw-snippet/{path.split('/')[-1]}?raw=inline",
            'dpaste.org': lambda: f"https://dpaste.org/{path.split('/')[-1]}/raw",
            'p.ip.fi': lambda: url,
            'pastesio.com': lambda: f"https://pastesio.com/raw/{path.split('/')[-1]}",
            'snippet.host': lambda: f"https://snippet.host/{path.split('/')[-1]}/raw",
            'paste.debian.net': lambda: f"https://paste.debian.net/plain/{path.split('/')[-1]}",
            'paste.centos.org': lambda: f"https://paste.centos.org/view/raw/{path.split('/')[-1]}",
            'paste-bin.xyz': lambda: f"https://paste-bin.xyz/raw/{path.split('/')[-1]}",
            'bitbin.it': lambda: f"https://bitbin.it/{path.split('/')[-1]}/raw",
            'bpa.st': lambda: f"https://bpa.st/raw/{path.split('/')[-1]}",
            'pastejustit.com': lambda: f"https://pastejustit.com/raw/{path.split('/')[-1]}",
            'cutapaste.net': lambda: f"https://cutapaste.net/raw/{path.split('/')[-1]}",
            'gist.github.com': lambda: f"{url.rstrip('/')}/raw",
            'ideone.com': lambda: f"https://ideone.com/plain/{path.split('/')[-1]}", 
            'paste.rohitab.com': lambda: f"http://paste.rohitab.com/download/{path.split('/')[-1]}", 
            'paste.opensuse.org': lambda: f"https://paste.opensuse.org/pastes/{path.split('/')[-1]}/raw"
        }
        

        
        # Check for conversions
        for conv_domain, converter in conversions.items():
            if conv_domain in domain:
                return converter()
        
        # No mapping found for this domain
        return False
        
    except Exception as e:
        logger.warning(f"Error converting URL to raw format: {e}")
        return False


@celery_app.task(bind=True)
def run_pastes_search_by_domain(self, domain: str, job_id: str = None, analyze_secrets: bool = False, chunk_size: int = 100, cse_page_limit: int = 10):
    """
    Search for pastes containing the specified domain using Google CSE API.

    Args:
        domain: Domain to search for
        job_id: Task ID (optional)
        analyze_secrets: Whether to fetch paste content and analyze for secrets
        chunk_size: Number of records per chunk for storage
        cse_page_limit: Maximum number of CSE pages to fetch (default: 10, max: 10)
    """
    try:
        # Create operation if needed
        if not job_id:
            job_id = operations.create_operation(
                feature="pastes_search",
                parameters={"domain": domain, "secrets": analyze_secrets, "cse_page_limit": cse_page_limit}
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting pastes search by domain'})

        # Initialize variables for pagination
        start_index = 1
        total_results = 0
        chunk_number = 0
        current_chunk = []
        paste_sources = set()
        date_range = {"min": None, "max": None}
        secrets_found = 0
        total_pastes_analyzed = 0

        # Process pages up to the specified limit (Google CSE allows up to 100 results total)
        # Ensure cse_page_limit doesn't exceed the maximum of 10 pages
        max_pages = min(cse_page_limit, 10)
        max_start_index = (max_pages - 1) * 10 + 1  # Calculate the last valid start_index
        
        try:
            while start_index <= max_start_index:
                # Query Google CSE API
                api_response = query_google_cse_api(domain, start_index)

                if not api_response or "items" not in api_response:
                    if start_index == 1:  # First request failed
                        # Update database with error
                        operations.update_operation(
                            job_id=job_id,
                            status=TaskStatus.FAILED,
                            error="No Results Found",
                            end_time=datetime.now()
                        )

                        return {
                            "job_id": job_id,
                            "success": False,
                            "message": "No Results Found"
                        }
                    else:  # We've already processed some data, so break the loop
                        break

                # Extract data from response
                items = api_response.get("items", [])
                search_info = api_response.get("searchInformation", {})
                
                # Check if we have results
                if not items:
                    break

                # Update progress
                results_count = len(items)
                total_results += results_count
                current_page = (start_index-1)//10 + 1
                progress_message = f"Processing page {current_page} of {max_pages}, found {total_results} results so far"
                progress_percentage = min(80, int((start_index / max_start_index) * 80))  # Cap at 80% for search phase
                self.update_state(state='STARTED', meta={'progress': progress_percentage, 'message': progress_message})

                # Process each paste result
                for item in items:
                    # Extract basic information
                    paste_record = {
                        "title": item.get("title", ""),
                        "link": item.get("link", ""),
                        "snippet": item.get("snippet", ""),
                    }

                    # Initialize secrets list
                    all_secrets = []

                    # Only perform secret detection if analyze_secrets is enabled
                    if analyze_secrets:
                        # Analyze snippet for secrets
                        snippet = item.get("snippet", "")
                        if snippet:
                            snippet_secrets = SecretDetection(snippet)
                            if snippet_secrets:
                                # Add source information to snippet secrets
                                for secret in snippet_secrets:
                                    secret['source'] = 'search_snippet'
                                all_secrets.extend(snippet_secrets)
                                secrets_found += len(snippet_secrets)

                    # Extract metadata
                    source_domain = urlparse(item.get("link", "")).netloc
                    if source_domain:
                        paste_sources.add(source_domain)


                    # If secrets analysis is requested, fetch and analyze paste content
                    if analyze_secrets:
                        paste_url = item.get("link", "")
                        raw_url = convert_to_raw_url(paste_url)
                        
                        if raw_url is not False:
                            content = fetch_paste_content(raw_url)
                            if content:
                                total_pastes_analyzed += 1
                                # Analyze content for secrets
                                content_secrets = SecretDetection(content)
                                if content_secrets:
                                    # Add source information to content secrets
                                    for secret in content_secrets:
                                        # Add source field
                                        secret['source'] = 'raw_file_content'
                                    all_secrets.extend(content_secrets)
                                    secrets_found += len(content_secrets)

                    # Set final secrets information
                    if all_secrets:
                        paste_record["secrets"] = all_secrets

                    # Add to current chunk
                    current_chunk.append(paste_record)

                    # If chunk is full, store it
                    if len(current_chunk) >= chunk_size:
                        operations.store_pastes_search_chunk(job_id, domain, chunk_number, current_chunk)
                        chunk_number += 1
                        current_chunk = []

                # Move to next page
                start_index += 10

                # Check if we've reached the total available results
                total_available = int(search_info.get("totalResults", "0"))
                if start_index > total_available:
                    break

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error during pastes search: {e}")
            # Update database with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=f"Network error: {str(e)}",
                end_time=datetime.now()
            )
            return {
                "job_id": job_id,
                "success": False,
                "message": f"Network error: {str(e)}"
            }

        except Exception as e:
            logger.exception(f"Error during pastes search: {e}")
            # Update database with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=str(e),
                end_time=datetime.now()
            )
            return {
                "job_id": job_id,
                "success": False,
                "message": str(e)
            }

        # Store any remaining records in the last chunk
        if current_chunk:
            operations.store_pastes_search_chunk(job_id, domain, chunk_number, current_chunk)
            chunk_number += 1

        # Update progress to 100%
        self.update_state(state='STARTED', meta={'progress': 100, 'message': 'Finalizing results'})

        # Prepare summary
        summary = {
            "total": total_results,
            "total_chunks": chunk_number,
            "chunk_size": chunk_size,
            "paste_sources": list(paste_sources),
            "unique_sources_count": len(paste_sources),
        }

        if analyze_secrets:
            summary.update({
                "total_pastes_analyzed": total_pastes_analyzed,
                "secrets_found": secrets_found,
                "pastes_with_secrets": sum(1 for chunk_num in range(chunk_number) 
                                         for record in operations.get_pastes_search_chunk(job_id, chunk_num).get("data", [])
                                         if record.get("secrets", []))
            })

        if date_range["min"] and date_range["max"]:
            summary["date_range"] = {
                "earliest": date_range["min"].isoformat(),
                "latest": date_range["max"].isoformat()
            }

        # Store results and mark as completed
        structured_output = {
            "stats": summary
        }

        # Use helper function for success
        result = handle_task_success(
            job_id=job_id,
            structured_output=structured_output,
            message=f"Successfully found {total_results} paste results for domain {domain}",
            count=total_results
        )
        result["summary"] = summary
        return result

    except Exception as e:
        # Use helper function for exceptions
        return handle_task_exception(job_id, e, f"pastes search task for domain {domain}")